const express = require('express');
const multer = require('multer');
const XLSX = require('xlsx');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const { sendBulkEmail, sendTestEmail } = require('./emailService');
const {
    initializeDatabase,
    createContactSet,
    insertContacts,
    createGroups,
    assignContactsToGroups,
    getAllContactSets,
    getAllContacts,
    getContactsBySet,
    getGroupsBySet,
    getContactsByGroup,
    clearAllContacts,
    getAllSentEmails,
    getSentEmailDetails
} = require('./database');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../')));

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = path.join(__dirname, 'uploads');
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        cb(null, Date.now() + '-' + file.originalname);
    }
});

// Multer configuration for Excel file uploads (contact imports)
const uploadExcel = multer({
    storage: storage,
    fileFilter: (req, file, cb) => {
        // Accept only Excel files
        const allowedTypes = [
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Only Excel files are allowed'), false);
        }
    },
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
    }
});

// Multer configuration for email attachments (allows images and other common file types)
const uploadAttachments = multer({
    storage: storage,
    fileFilter: (req, file, cb) => {
        // Accept common file types for email attachments
        const allowedTypes = [
            // Images
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/bmp',
            'image/webp',
            // Documents
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            // Text files
            'text/plain',
            'text/csv',
            // Archives
            'application/zip',
            'application/x-rar-compressed'
        ];
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error(`File type ${file.mimetype} is not allowed for email attachments`), false);
        }
    },
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
    }
});

// Helper function to parse Excel file
function parseExcelFile(filePath) {
    try {
        const workbook = XLSX.readFile(filePath);
        const sheetName = workbook.SheetNames[0]; // Use first sheet
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        // Map Excel columns to our database fields
        const contacts = jsonData.map(row => {
            return {
                principal_name: row['Principal Name'] || row['principal name'] || row['PRINCIPAL NAME'] ||
                               row.PrincipalName || row.principalName || row.PRINCIPALNAME || '',
                email: row.Email || row.email || row.EMAIL || '',
                gde_email: row.GDEemail || row.gdeemail || row.GDEEMAIL ||
                          row['GDE email'] || row['gde email'] || row['GDE EMAIL'] || '',
                cell_phone: row['Cell Phone'] || row['cell phone'] || row['CELL PHONE'] ||
                           row.CellPhone || row.cellPhone || row.CELLPHONE ||
                           row.Phone || row.phone || row.PHONE || '',
                notes: row.Notes || row.notes || row.NOTES || ''
            };
        });

        return contacts;
    } catch (error) {
        throw new Error('Failed to parse Excel file: ' + error.message);
    }
}

// Routes

// Serve the main HTML file
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../index.html'));
});

// Upload and process Excel file
app.post('/api/upload-excel', uploadExcel.single('excelFile'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'No file uploaded' });
        }

        const filePath = req.file.path;
        const filename = req.file.originalname;

        // Parse Excel file
        const contacts = parseExcelFile(filePath);

        if (contacts.length === 0) {
            return res.status(400).json({ error: 'No valid data found in Excel file' });
        }

        // Create a new contact set
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const contactSetName = `${filename.replace(/\.[^/.]+$/, "")} - ${timestamp}`;
        const contactSet = await createContactSet(contactSetName, filename);

        // Insert contacts into database with contact set ID
        const result = await insertContacts(contacts, contactSet.id);

        // Clean up uploaded file
        fs.unlinkSync(filePath);

        res.json({
            message: 'Excel file processed successfully',
            insertedCount: result.insertedCount,
            totalRows: contacts.length,
            contactSetId: contactSet.id,
            contactSetName: contactSetName
        });

    } catch (error) {
        console.error('Error processing Excel file:', error);

        // Clean up uploaded file if it exists
        if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }

        res.status(500).json({ error: error.message });
    }
});

// Get all contacts
app.get('/api/contacts', async (req, res) => {
    try {
        const contacts = await getAllContacts();
        res.json(contacts);
    } catch (error) {
        console.error('Error fetching contacts:', error);
        res.status(500).json({ error: 'Failed to fetch contacts' });
    }
});

// Get all contact sets
app.get('/api/contact-sets', async (req, res) => {
    try {
        const contactSets = await getAllContactSets();
        res.json(contactSets);
    } catch (error) {
        console.error('Error fetching contact sets:', error);
        res.status(500).json({ error: 'Failed to fetch contact sets' });
    }
});

// Get contacts by contact set
app.get('/api/contact-sets/:id/contacts', async (req, res) => {
    try {
        const contactSetId = parseInt(req.params.id);
        const contacts = await getContactsBySet(contactSetId);
        res.json(contacts);
    } catch (error) {
        console.error('Error fetching contacts by set:', error);
        res.status(500).json({ error: 'Failed to fetch contacts' });
    }
});

// Get groups by contact set
app.get('/api/contact-sets/:id/groups', async (req, res) => {
    try {
        const contactSetId = parseInt(req.params.id);
        const groups = await getGroupsBySet(contactSetId);
        res.json(groups);
    } catch (error) {
        console.error('Error fetching groups by set:', error);
        res.status(500).json({ error: 'Failed to fetch groups' });
    }
});

// Get contacts by group
app.get('/api/groups/:id/contacts', async (req, res) => {
    try {
        const groupId = parseInt(req.params.id);
        const contacts = await getContactsByGroup(groupId);
        res.json(contacts);
    } catch (error) {
        console.error('Error fetching contacts by group:', error);
        res.status(500).json({ error: 'Failed to fetch contacts by group' });
    }
});

// Create groups for a contact set
app.post('/api/contact-sets/:id/create-groups', async (req, res) => {
    try {
        const contactSetId = parseInt(req.params.id);
        const { groupSize } = req.body;

        if (!groupSize || groupSize < 1) {
            return res.status(400).json({ error: 'Valid group size is required' });
        }

        // Create groups
        const groups = await createGroups(contactSetId, groupSize);

        // Assign contacts to groups
        await assignContactsToGroups(contactSetId, groupSize);

        res.json({
            message: 'Groups created successfully',
            groups: groups,
            groupCount: groups.length
        });

    } catch (error) {
        console.error('Error creating groups:', error);
        res.status(500).json({ error: 'Failed to create groups' });
    }
});

// Send bulk email to group
app.post('/api/groups/:id/send-email', uploadAttachments.array('attachments', 10), async (req, res) => {
    console.log('=== EMAIL SEND REQUEST RECEIVED ===');
    console.log('Timestamp:', new Date().toISOString());
    console.log('Group ID:', req.params.id);
    console.log('Request headers:', {
        'content-type': req.headers['content-type'],
        'content-length': req.headers['content-length'],
        'user-agent': req.headers['user-agent']
    });
    console.log('Request body:', req.body);
    console.log('Files:', req.files);
    console.log('Request method:', req.method);
    console.log('Request URL:', req.url);

    try {
        console.log('=== STEP 1: PARSING PARAMETERS ===');
        const groupId = parseInt(req.params.id);
        const { subject, message } = req.body;

        console.log('Parsed groupId:', groupId, 'Type:', typeof groupId);
        console.log('Subject:', subject, 'Type:', typeof subject);
        console.log('Message:', message ? `${message.substring(0, 100)}...` : 'undefined', 'Type:', typeof message);
        console.log('Message length:', message ? message.length : 'undefined');

        if (!subject || !message) {
            console.log('=== ERROR: Missing subject or message ===');
            console.log('Subject check:', !subject, 'Message check:', !message);
            return res.status(400).json({ error: 'Subject and message are required' });
        }

        console.log('=== STEP 2: GETTING CONTACTS FOR GROUP ===');
        console.log('Getting contacts for group:', groupId);
        // Get contacts for this group
        const contacts = await getContactsByGroup(groupId);
        console.log('Found contacts:', contacts.length);
        console.log('Sample contacts:', contacts.slice(0, 3).map(c => ({ id: c.id, email: c.email, name: c.principal_name })));

        if (contacts.length === 0) {
            console.log('=== ERROR: No contacts found in group ===');
            return res.status(400).json({ error: 'No contacts found in this group' });
        }

        console.log('=== STEP 3: FILTERING VALID EMAIL ADDRESSES ===');
        // Filter out contacts without email addresses
        const validContacts = contacts.filter(contact => contact.email && contact.email.trim() !== '');
        console.log('Valid contacts with emails:', validContacts.length);
        console.log('Invalid contacts:', contacts.length - validContacts.length);

        // Log some examples of invalid contacts
        const invalidContacts = contacts.filter(contact => !contact.email || contact.email.trim() === '');
        if (invalidContacts.length > 0) {
            console.log('Sample invalid contacts:', invalidContacts.slice(0, 3).map(c => ({ id: c.id, email: c.email, name: c.principal_name })));
        }

        if (validContacts.length === 0) {
            console.log('=== ERROR: No valid email addresses found ===');
            return res.status(400).json({ error: 'No valid email addresses found in this group' });
        }

        console.log('=== STEP 4: STARTING BULK EMAIL SEND ===');
        console.log('About to send emails to:', validContacts.length, 'recipients');
        console.log('Subject:', subject);
        console.log('Message preview:', message.substring(0, 100) + '...');
        console.log('Attachments:', req.files ? req.files.length : 0);

        // Get contact set ID from the first contact (all contacts in a group have the same contact_set_id)
        const contactSetId = validContacts.length > 0 ? validContacts[0].contact_set_id : null;
        console.log('Contact Set ID:', contactSetId);

        // Send bulk email
        const results = await sendBulkEmail(validContacts, subject, message, req.files || [], groupId, contactSetId);
        console.log('=== STEP 5: BULK EMAIL COMPLETED ===');
        console.log('Bulk email results:', results);

        res.json({
            message: 'Bulk email sending completed',
            total: results.total,
            successful: results.successful.length,
            failed: results.failed.length,
            results: results
        });

    } catch (error) {
        console.error('=== CRITICAL ERROR SENDING BULK EMAIL ===');
        console.error('Timestamp:', new Date().toISOString());
        console.error('Error type:', error.constructor.name);
        console.error('Error message:', error.message);
        console.error('Error code:', error.code);
        console.error('Error stack:', error.stack);
        console.error('Full error object:', error);
        console.error('Error details:', {
            message: error.message,
            code: error.code,
            errno: error.errno,
            syscall: error.syscall,
            name: error.name,
            response: error.response,
            responseCode: error.responseCode,
            command: error.command
        });

        // Log request details for debugging
        console.error('Request details at error:', {
            groupId: req.params.id,
            hasSubject: !!req.body.subject,
            hasMessage: !!req.body.message,
            filesCount: req.files ? req.files.length : 0
        });

        // Clean up uploaded files if there's an error
        if (req.files) {
            console.log('Cleaning up', req.files.length, 'uploaded files');
            req.files.forEach(file => {
                try {
                    if (fs.existsSync(file.path)) {
                        fs.unlinkSync(file.path);
                        console.log('Cleaned up file:', file.path);
                    }
                } catch (cleanupError) {
                    console.error('Error cleaning up file:', file.path, cleanupError);
                }
            });
        }

        // Determine error type and provide specific message
        let errorMessage = 'Failed to send bulk email';
        if (error.code === 'EAUTH') {
            errorMessage = 'Email authentication failed. Check your email credentials.';
        } else if (error.code === 'ECONNECTION' || error.code === 'ENOTFOUND') {
            errorMessage = 'Cannot connect to email server. Check your email server settings.';
        } else if (error.code === 'ETIMEDOUT') {
            errorMessage = 'Email server connection timed out.';
        } else if (error.message && error.message.includes('Invalid login')) {
            errorMessage = 'Invalid email login credentials.';
        }

        res.status(500).json({
            error: errorMessage,
            details: error.message,
            code: error.code,
            timestamp: new Date().toISOString()
        });
    }
});

// Create demo contact set
app.post('/api/create-demo-data', async (req, res) => {
    try {
        // Create demo contact set
        const contactSet = await createContactSet('Demo Contact Set - Testing', 'demo_contacts.xlsx');

        // Demo contacts data
        const demoContacts = [
            { principal_name: 'John Smith', email: '<EMAIL>', gde_email: '<EMAIL>', cell_phone: '0123456789' },
            { principal_name: 'Jane Doe', email: '<EMAIL>', gde_email: '<EMAIL>', cell_phone: '0987654321' },
            { principal_name: 'Mike Johnson', email: '<EMAIL>', gde_email: '', cell_phone: '0111222333' },
            { principal_name: 'Sarah Wilson', email: '<EMAIL>', gde_email: '<EMAIL>', cell_phone: '0444555666' },
            { principal_name: 'David Brown', email: '<EMAIL>', gde_email: '', cell_phone: '0777888999' },
            { principal_name: 'Lisa Davis', email: '<EMAIL>', gde_email: '<EMAIL>', cell_phone: '0222333444' },
            { principal_name: 'Tom Anderson', email: '<EMAIL>', gde_email: '', cell_phone: '0555666777' },
            { principal_name: 'Emma Taylor', email: '<EMAIL>', gde_email: '<EMAIL>', cell_phone: '0888999000' },
            { principal_name: 'Chris Martin', email: '<EMAIL>', gde_email: '', cell_phone: '0333444555' },
            { principal_name: 'Amy White', email: '<EMAIL>', gde_email: '<EMAIL>', cell_phone: '0666777888' }
        ];

        // Insert demo contacts
        const result = await insertContacts(demoContacts, contactSet.id);

        res.json({
            message: 'Demo contact set created successfully',
            contactSetId: contactSet.id,
            contactSetName: contactSet.name,
            insertedCount: result.insertedCount,
            totalContacts: demoContacts.length
        });

    } catch (error) {
        console.error('Error creating demo data:', error);
        res.status(500).json({ error: 'Failed to create demo data' });
    }
});

// Send test email
app.post('/api/send-test-email', async (req, res) => {
    try {
        const { to, subject, message } = req.body;

        if (!to) {
            return res.status(400).json({ error: 'Recipient email is required' });
        }

        const result = await sendTestEmail(to, subject, message);

        if (result.success) {
            res.json({ message: 'Test email sent successfully', messageId: result.messageId });
        } else {
            res.status(500).json({ error: result.error });
        }

    } catch (error) {
        console.error('Error sending test email:', error);
        res.status(500).json({ error: 'Failed to send test email' });
    }
});

// Clear all contacts
app.delete('/api/contacts', async (req, res) => {
    try {
        const result = await clearAllContacts();
        res.json({
            message: 'All contacts cleared successfully',
            deletedContacts: result.deletedContacts,
            deletedGroups: result.deletedGroups,
            deletedSets: result.deletedSets
        });
    } catch (error) {
        console.error('Error clearing contacts:', error);
        res.status(500).json({ error: 'Failed to clear contacts' });
    }
});

// Get sent email history
app.get('/api/sent-emails', async (req, res) => {
    try {
        const limit = parseInt(req.query.limit) || 50;
        const offset = parseInt(req.query.offset) || 0;

        const sentEmails = await getAllSentEmails(limit, offset);
        res.json({
            emails: sentEmails,
            limit: limit,
            offset: offset,
            hasMore: sentEmails.length === limit
        });
    } catch (error) {
        console.error('Error fetching sent emails:', error);
        res.status(500).json({ error: 'Failed to fetch sent emails' });
    }
});

// Get detailed information about a specific sent email
app.get('/api/sent-emails/:id', async (req, res) => {
    try {
        const emailId = parseInt(req.params.id);
        const emailDetails = await getSentEmailDetails(emailId);

        if (!emailDetails) {
            return res.status(404).json({ error: 'Email not found' });
        }

        res.json(emailDetails);
    } catch (error) {
        console.error('Error fetching email details:', error);
        res.status(500).json({ error: 'Failed to fetch email details' });
    }
});

// Helper function to generate demo email campaigns
function generateDemoEmails(contacts, contactSets) {
    const demoSubjects = [
        'Welcome to Our New Platform!',
        'Important Updates for This Quarter',
        'Monthly Newsletter - March 2024',
        'Invitation to Annual Conference',
        'System Maintenance Notification',
        'New Features Available Now',
        'Thank You for Your Continued Support',
        'Upcoming Training Sessions',
        'Policy Updates and Changes',
        'Special Offer - Limited Time',
        'Weekly Report Summary',
        'Project Milestone Achieved',
        'Customer Satisfaction Survey',
        'Holiday Schedule Announcement',
        'Security Update Required'
    ];

    const demoMessages = [
        'Dear Team,\n\nWe are excited to announce the launch of our new platform. This update brings enhanced features and improved user experience.\n\nBest regards,\nThe Team',
        'Hello Everyone,\n\nPlease find attached the quarterly updates and important information for the upcoming period.\n\nThank you for your attention.\n\nRegards,\nManagement',
        'Hi there,\n\nOur monthly newsletter is here with the latest news, updates, and insights from our organization.\n\nStay informed and engaged!\n\nBest wishes,\nCommunications Team',
        'Dear Colleagues,\n\nYou are cordially invited to attend our annual conference. This year\'s theme focuses on innovation and growth.\n\nLooking forward to seeing you there!\n\nConference Committee',
        'Important Notice,\n\nScheduled system maintenance will occur this weekend. Please plan accordingly and save your work.\n\nThank you for your cooperation.\n\nIT Department'
    ];

    const demoEmails = [];
    const emailsToGenerate = Math.min(15, Math.max(5, Math.floor(contacts.length / 3)));

    for (let i = 0; i < emailsToGenerate; i++) {
        const randomDate = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000); // Last 30 days
        const subject = demoSubjects[Math.floor(Math.random() * demoSubjects.length)];
        const message = demoMessages[Math.floor(Math.random() * demoMessages.length)];

        // Randomly select contacts for this email (between 5 and all contacts)
        const minRecipients = Math.min(5, contacts.length);
        const maxRecipients = contacts.length;
        const recipientCount = Math.floor(Math.random() * (maxRecipients - minRecipients + 1)) + minRecipients;

        const shuffledContacts = [...contacts].sort(() => 0.5 - Math.random());
        const emailContacts = shuffledContacts.slice(0, recipientCount);

        // Calculate success metrics (90-95% success rate)
        const successRate = 0.9 + Math.random() * 0.05;
        const successfulCount = Math.floor(emailContacts.length * successRate);
        const failedCount = emailContacts.length - successfulCount;

        // Random contact set or group
        const contactSet = contactSets[Math.floor(Math.random() * contactSets.length)];

        // Random attachments
        const hasAttachments = Math.random() > 0.7; // 30% chance of attachments
        const attachmentCount = hasAttachments ? Math.floor(Math.random() * 3) + 1 : 0;
        const attachmentNames = hasAttachments ?
            ['document.pdf', 'presentation.pptx', 'spreadsheet.xlsx'].slice(0, attachmentCount).join(', ') : '';

        demoEmails.push({
            id: i + 1,
            group_id: null,
            contact_set_id: contactSet?.id || null,
            subject: subject,
            message: message,
            sender_email: '<EMAIL>',
            total_recipients: emailContacts.length,
            successful_count: successfulCount,
            failed_count: failedCount,
            attachment_count: attachmentCount,
            attachment_names: attachmentNames,
            status: 'completed',
            started_at: randomDate.toISOString(),
            completed_at: new Date(randomDate.getTime() + Math.random() * 60 * 60 * 1000).toISOString(), // Completed within an hour
            created_at: randomDate.toISOString(),
            group_name: null,
            contact_set_name: contactSet?.name || 'Direct Send',
            emailContacts: emailContacts // Store for detailed view
        });
    }

    return demoEmails.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
}

// Helper function to get contacts for a specific email
function getEmailContacts(contacts, emailDetails) {
    return emailDetails.emailContacts || contacts.slice(0, emailDetails.total_recipients);
}

// Demo endpoint - Generate fake sent emails using real contacts
app.get('/api/demo/sent-emails', async (req, res) => {
    try {
        const limit = parseInt(req.query.limit) || 50;
        const offset = parseInt(req.query.offset) || 0;

        // Get all contacts and contact sets from database
        const contacts = await getAllContacts();
        const contactSets = await getAllContactSets();

        if (contacts.length === 0) {
            return res.json({ emails: [], limit, offset, hasMore: false });
        }

        // Generate demo email campaigns
        const demoEmails = generateDemoEmails(contacts, contactSets);

        // Apply pagination
        const paginatedEmails = demoEmails.slice(offset, offset + limit);
        const hasMore = offset + limit < demoEmails.length;

        res.json({
            emails: paginatedEmails,
            limit: limit,
            offset: offset,
            hasMore: hasMore
        });
    } catch (error) {
        console.error('Error generating demo emails:', error);
        res.status(500).json({ error: 'Failed to generate demo emails' });
    }
});

// Demo endpoint - Get detailed information about a demo sent email
app.get('/api/demo/sent-emails/:id', async (req, res) => {
    try {
        const emailId = parseInt(req.params.id);

        // Get all contacts for generating demo data
        const contacts = await getAllContacts();
        const contactSets = await getAllContactSets();

        if (contacts.length === 0) {
            return res.status(404).json({ error: 'No contacts available for demo' });
        }

        // Generate demo emails and find the requested one
        const demoEmails = generateDemoEmails(contacts, contactSets);
        const emailDetails = demoEmails.find(email => email.id === emailId);

        if (!emailDetails) {
            return res.status(404).json({ error: 'Demo email not found' });
        }

        // Generate detailed recipients data for this email
        const emailContacts = getEmailContacts(contacts, emailDetails);
        emailDetails.recipients = emailContacts.map(contact => ({
            id: contact.id,
            contact_id: contact.id,
            recipient_email: contact.email,
            recipient_name: contact.principal_name,
            status: Math.random() > 0.1 ? 'sent' : 'failed', // 90% success rate
            message_id: Math.random() > 0.1 ? `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}` : null,
            error_message: Math.random() > 0.1 ? null : 'SMTP connection timeout',
            sent_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
        }));

        res.json(emailDetails);
    } catch (error) {
        console.error('Error fetching demo email details:', error);
        res.status(500).json({ error: 'Failed to fetch demo email details' });
    }
});

// Error handling middleware
app.use((error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({ error: 'File too large. Maximum size is 10MB.' });
        }
    }
    res.status(500).json({ error: error.message });
});

// Global error handlers
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    console.error('Stack:', error.stack);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Initialize database and start server
async function startServer() {
    try {
        await initializeDatabase();
        app.listen(PORT, () => {
            console.log(`Server running on http://localhost:${PORT}`);
        });
    } catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
}

startServer();