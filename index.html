<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi Email Sender</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .upload-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .upload-section h2 {
            margin-bottom: 1rem;
            color: #333;
        }

        .file-upload {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            transition: border-color 0.3s ease;
        }

        .file-upload:hover {
            border-color: #667eea;
        }

        .file-upload.dragover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: transform 0.2s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
        }

        .upload-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .file-info {
            margin-top: 1rem;
            padding: 1rem;
            background-color: #f8f9fa;
            border-radius: 5px;
            display: none;
        }

        .controls {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        .btn:hover {
            transform: translateY(-1px);
            opacity: 0.9;
        }

        .contacts-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .contacts-header {
            background-color: #f8f9fa;
            padding: 1.5rem;
            border-bottom: 1px solid #dee2e6;
        }

        .contacts-header h2 {
            margin: 0;
            color: #333;
        }

        .contacts-count {
            color: #666;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        .table-container {
            overflow-x: auto;
            max-height: 600px;
            overflow-y: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        th {
            background-color: #f8f9fa;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        tr:hover {
            background-color: #f8f9ff;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #666;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .alert-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        /* Email Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 10px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            background: none;
            border: none;
        }

        .close:hover {
            opacity: 0.7;
        }

        .modal-body {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            font-family: inherit;
        }

        .form-group textarea {
            min-height: 120px;
            resize: vertical;
        }

        .file-upload-area {
            border: 2px dashed #ddd;
            border-radius: 5px;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s ease;
        }

        .file-upload-area:hover {
            border-color: #667eea;
        }

        .file-upload-area.dragover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }

        .attached-files {
            margin-top: 1rem;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            background-color: #f8f9fa;
            border-radius: 3px;
            margin-bottom: 0.5rem;
        }

        .file-item button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 2px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
        }

        .modal-footer {
            padding: 1.5rem;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .demo-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .demo-section h3 {
            color: #856404;
            margin-bottom: 1rem;
        }

        /* Email History Styles */
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
            white-space: nowrap;
        }

        .status-success {
            background-color: #d4edda;
            color: #155724;
        }

        .status-warning {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }

        .status-info {
            background-color: #d1ecf1;
            color: #0c5460;
        }

        .status-secondary {
            background-color: #e2e3e5;
            color: #383d41;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 0.8rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .table-container {
                font-size: 0.9rem;
            }

            th, td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Multi Email Sender</h1>
            <p>Import contacts from Excel and manage your email campaigns</p>
        </div>

        <div id="alerts"></div>

        <!-- Demo Section -->
        <div class="demo-section">
            <h3>🧪 Demo Mode</h3>
            <p>Create a demo contact set with sample data for testing the email functionality</p>
            <button class="btn btn-primary" onclick="createDemoData()">Create Demo Contact Set</button>
        </div>

        <div class="upload-section">
            <h2>Upload Excel File</h2>
            <div class="file-upload" id="fileUpload">
                <p>Drag and drop your Excel file here or click to browse</p>
                <p style="font-size: 0.9rem; color: #666; margin-top: 0.5rem;">
                    Supported formats: .xlsx, .xls (Max size: 10MB)<br>
                    Expected columns: Principal Name, Email, GDEemail, Cell Phone
                </p>
                <input type="file" id="fileInput" class="file-input" accept=".xlsx,.xls">
                <button type="button" class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    Choose File
                </button>
            </div>
            <div class="file-info" id="fileInfo"></div>
        </div>

        <div class="controls">
            <button class="btn btn-primary" onclick="loadContactSets()">Refresh Data</button>
            <button class="btn btn-danger" onclick="clearAllContacts()">Clear All Data</button>
            <span id="contactsCount" class="contacts-count"></span>
        </div>

        <!-- Contact Sets Section -->
        <div class="contacts-section" style="margin-bottom: 2rem;">
            <div class="contacts-header">
                <h2>Contact Sets</h2>
                <div class="contacts-count" id="contactSetsCountHeader">No contact sets loaded</div>
            </div>
            <div class="table-container">
                <div id="contactSetsTable">
                    <div class="empty-state" id="contactSetsEmptyState">
                        <div style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;">📁</div>
                        <h3>No contact sets yet</h3>
                        <p>Upload an Excel file to create your first contact set</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Groups Section -->
        <div class="contacts-section" id="groupsSection" style="margin-bottom: 2rem; display: none;">
            <div class="contacts-header">
                <h2>Groups</h2>
                <div class="contacts-count" id="groupsCountHeader">No groups created</div>
                <div style="margin-top: 1rem;">
                    <input type="number" id="groupSizeInput" placeholder="Group size (e.g., 50)" min="1" style="padding: 8px; margin-right: 10px; border: 1px solid #ddd; border-radius: 4px;">
                    <button class="btn btn-primary" onclick="createGroups()">Create Groups</button>
                </div>
            </div>
            <div class="table-container">
                <div id="groupsTable">
                    <div class="empty-state" id="groupsEmptyState">
                        <div style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;">👥</div>
                        <h3>No groups created</h3>
                        <p>Select a contact set and specify group size to create groups</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="contacts-section">
            <div class="contacts-header">
                <h2>Imported Contacts</h2>
                <div class="contacts-count" id="contactsCountHeader">No contacts loaded</div>
            </div>
            <div class="table-container">
                <div id="contactsTable">
                    <div class="loading" id="loadingState" style="display: none;">
                        <div class="spinner"></div>
                        <p>Loading contacts...</p>
                    </div>
                    <div class="empty-state" id="emptyState">
                        <div style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;">📊</div>
                        <h3>No contacts yet</h3>
                        <p>Upload an Excel file to get started</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Email History Section -->
    <div class="contacts-section" style="margin-bottom: 2rem;">
        <div class="contacts-header">
            <h2>📧 Email History</h2>
            <div class="contacts-count" id="emailHistoryCountHeader">Loading email history...</div>
            <div style="margin-top: 1rem;">
                <button class="btn btn-primary" onclick="refreshEmailHistory()">🔄 Refresh</button>
                <button class="btn btn-secondary" onclick="loadMoreEmails()" id="loadMoreBtn" style="display: none;">Load More</button>
            </div>
        </div>
        <div class="table-container">
            <div class="table-wrapper">
                <table id="emailHistoryTable">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Subject</th>
                            <th>Recipients</th>
                            <th>Status</th>
                            <th>Success Rate</th>
                            <th>Group/Set</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="emailHistoryTableBody">
                        <tr>
                            <td colspan="7" style="text-align: center; padding: 2rem; color: #666;">
                                Loading email history...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Email Modal -->
    <div id="emailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Send Bulk Email</h2>
                <button class="close" onclick="closeEmailModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="emailSubject">Subject *</label>
                    <input type="text" id="emailSubject" placeholder="Enter email subject">
                </div>

                <div class="form-group">
                    <label for="emailMessage">Message *</label>
                    <textarea id="emailMessage" placeholder="Enter your email message here..."></textarea>
                </div>

                <div class="form-group">
                    <label>Attachments (Optional)</label>
                    <div class="file-upload-area" id="attachmentUpload">
                        <p>Click here or drag files to attach</p>
                        <p style="font-size: 0.9rem; color: #666;">Max 10 files, 10MB each</p>
                    </div>
                    <input type="file" id="attachmentInput" multiple style="display: none;">
                    <div id="attachedFiles" class="attached-files"></div>
                </div>

                <div class="form-group">
                    <div style="background-color: #f8f9fa; padding: 1rem; border-radius: 5px;">
                        <strong>Recipients:</strong> <span id="recipientCount">0</span> contacts
                        <div id="recipientPreview" style="font-size: 0.9rem; color: #666; margin-top: 0.5rem;"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeEmailModal()">Cancel</button>
                <button class="btn btn-primary" onclick="sendBulkEmail()" id="sendEmailBtn">Send Email</button>
            </div>
        </div>
    </div>

    <!-- Email Details Modal -->
    <div id="emailDetailsModal" class="modal">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h2>📧 Email Details</h2>
                <button class="close" onclick="closeEmailDetailsModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div id="emailDetailsContent">
                    <div style="text-align: center; padding: 2rem; color: #666;">
                        Loading email details...
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeEmailDetailsModal()">Close</button>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let contacts = [];
        let contactSets = [];
        let selectedContactSetId = null;
        let groups = [];
        let sentEmails = [];
        let emailHistoryOffset = 0;
        let emailHistoryLimit = 20;

        // API base URL - change this if your backend runs on a different port
        const API_BASE_URL = window.location.hostname === 'localhost' && window.location.port === '3000'
            ? '' // Use relative URLs when served from Express
            : 'http://localhost:3000'; // Use absolute URL when served from different server

        // DOM elements
        const fileInput = document.getElementById('fileInput');
        const fileUpload = document.getElementById('fileUpload');
        const fileInfo = document.getElementById('fileInfo');
        const alertsContainer = document.getElementById('alerts');
        const contactsTable = document.getElementById('contactsTable');
        const loadingState = document.getElementById('loadingState');
        const emptyState = document.getElementById('emptyState');
        const contactsCount = document.getElementById('contactsCount');
        const contactsCountHeader = document.getElementById('contactsCountHeader');

        // New DOM elements for groups functionality
        const contactSetsTable = document.getElementById('contactSetsTable');
        const contactSetsEmptyState = document.getElementById('contactSetsEmptyState');
        const contactSetsCountHeader = document.getElementById('contactSetsCountHeader');
        const groupsSection = document.getElementById('groupsSection');
        const groupsTable = document.getElementById('groupsTable');
        const groupsEmptyState = document.getElementById('groupsEmptyState');
        const groupsCountHeader = document.getElementById('groupsCountHeader');
        const groupSizeInput = document.getElementById('groupSizeInput');

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            loadContactSets();
        });

        function setupEventListeners() {
            // File input change
            fileInput.addEventListener('change', handleFileSelect);

            // Drag and drop
            fileUpload.addEventListener('dragover', handleDragOver);
            fileUpload.addEventListener('dragleave', handleDragLeave);
            fileUpload.addEventListener('drop', handleFileDrop);
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                displayFileInfo(file);
                uploadFile(file);
            }
        }

        function handleDragOver(event) {
            event.preventDefault();
            fileUpload.classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.preventDefault();
            fileUpload.classList.remove('dragover');
        }

        function handleFileDrop(event) {
            event.preventDefault();
            fileUpload.classList.remove('dragover');

            const files = event.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];
                fileInput.files = files;
                displayFileInfo(file);
                uploadFile(file);
            }
        }

        function displayFileInfo(file) {
            const fileSize = (file.size / 1024 / 1024).toFixed(2);
            fileInfo.innerHTML = `
                <strong>Selected file:</strong> ${file.name}<br>
                <strong>Size:</strong> ${fileSize} MB<br>
                <strong>Type:</strong> ${file.type}
            `;
            fileInfo.style.display = 'block';
        }

        async function uploadFile(file) {
            const formData = new FormData();
            formData.append('excelFile', file);

            try {
                showAlert('Uploading and processing file...', 'info');

                const response = await fetch(`${API_BASE_URL}/api/upload-excel`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (response.ok) {
                    showAlert(
                        `Success! Imported ${result.insertedCount} contacts from ${result.totalRows} rows into "${result.contactSetName}".`,
                        'success'
                    );
                    loadContactSets();

                    // Clear file input
                    fileInput.value = '';
                    fileInfo.style.display = 'none';
                } else {
                    showAlert(`Error: ${result.error}`, 'error');
                }
            } catch (error) {
                console.error('Upload error:', error);
                showAlert('Failed to upload file. Please try again.', 'error');
            }
        }

        async function loadContactSets() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/contact-sets`);
                const data = await response.json();

                if (response.ok) {
                    contactSets = data;
                    displayContactSets(contactSets);
                    updateContactSetsCount(contactSets.length);

                    // Also load all contacts for the main table
                    loadContacts();
                } else {
                    showAlert(`Error loading contact sets: ${data.error}`, 'error');
                }
            } catch (error) {
                console.error('Load contact sets error:', error);
                showAlert('Failed to load contact sets. Please try again.', 'error');
            }
        }

        async function loadContacts() {
            try {
                showLoading(true);

                const response = await fetch(`${API_BASE_URL}/api/contacts`);
                const data = await response.json();

                if (response.ok) {
                    contacts = data;
                    displayContacts(contacts);
                    updateContactsCount(contacts.length);
                } else {
                    showAlert(`Error loading contacts: ${data.error}`, 'error');
                }
            } catch (error) {
                console.error('Load contacts error:', error);
                showAlert('Failed to load contacts. Please try again.', 'error');
            } finally {
                showLoading(false);
            }
        }

        async function loadContactsBySet(contactSetId) {
            try {
                showLoading(true);

                const response = await fetch(`${API_BASE_URL}/api/contact-sets/${contactSetId}/contacts`);
                const data = await response.json();

                if (response.ok) {
                    contacts = data;
                    displayContacts(contacts);
                    updateContactsCount(contacts.length);
                } else {
                    showAlert(`Error loading contacts: ${data.error}`, 'error');
                }
            } catch (error) {
                console.error('Load contacts by set error:', error);
                showAlert('Failed to load contacts. Please try again.', 'error');
            } finally {
                showLoading(false);
            }
        }

        async function loadGroups(contactSetId) {
            try {
                const response = await fetch(`${API_BASE_URL}/api/contact-sets/${contactSetId}/groups`);
                const data = await response.json();

                if (response.ok) {
                    groups = data;
                    displayGroups(groups);
                    updateGroupsCount(groups.length);
                    groupsSection.style.display = 'block';
                } else {
                    showAlert(`Error loading groups: ${data.error}`, 'error');
                }
            } catch (error) {
                console.error('Load groups error:', error);
                showAlert('Failed to load groups. Please try again.', 'error');
            }
        }

        async function loadContactsByGroup(groupId) {
            try {
                showLoading(true);

                const response = await fetch(`${API_BASE_URL}/api/groups/${groupId}/contacts`);
                const data = await response.json();

                if (response.ok) {
                    contacts = data;
                    displayContacts(contacts);
                    updateContactsCount(contacts.length);
                } else {
                    showAlert(`Error loading contacts by group: ${data.error}`, 'error');
                }
            } catch (error) {
                console.error('Load contacts by group error:', error);
                showAlert('Failed to load contacts by group. Please try again.', 'error');
            } finally {
                showLoading(false);
            }
        }

        function displayContactSets(contactSetsData) {
            if (contactSetsData.length === 0) {
                contactSetsTable.innerHTML = '';
                contactSetsTable.appendChild(contactSetsEmptyState);
                return;
            }

            const table = document.createElement('table');
            table.innerHTML = `
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Filename</th>
                        <th>Total Contacts</th>
                        <th>Groups</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${contactSetsData.map(set => `
                        <tr>
                            <td>${set.name}</td>
                            <td>${set.filename || '-'}</td>
                            <td>${set.total_contacts}</td>
                            <td>${set.group_count}</td>
                            <td>${new Date(set.created_at).toLocaleDateString()}</td>
                            <td>
                                <button class="btn btn-primary" style="font-size: 0.8rem; padding: 5px 10px;" onclick="selectContactSet(${set.id})">View</button>
                                ${set.group_count === 0 ?
                                    `<button class="btn btn-primary" style="font-size: 0.8rem; padding: 5px 10px; margin-left: 5px;" onclick="showGroupCreation(${set.id})">Create Groups</button>` :
                                    `<button class="btn btn-primary" style="font-size: 0.8rem; padding: 5px 10px; margin-left: 5px;" onclick="viewGroups(${set.id})">View Groups</button>`
                                }
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            `;

            contactSetsTable.innerHTML = '';
            contactSetsTable.appendChild(table);
        }

        function displayGroups(groupsData) {
            if (groupsData.length === 0) {
                groupsTable.innerHTML = '';
                groupsTable.appendChild(groupsEmptyState);
                return;
            }

            const table = document.createElement('table');
            table.innerHTML = `
                <thead>
                    <tr>
                        <th>Group Number</th>
                        <th>Group Name</th>
                        <th>Expected Size</th>
                        <th>Actual Contacts</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${groupsData.map(group => `
                        <tr style="cursor: pointer;" onclick="viewGroupContacts(${group.id}, '${group.name}')">
                            <td>${group.group_number}</td>
                            <td>${group.name}</td>
                            <td>${group.size}</td>
                            <td>${group.actual_contact_count}</td>
                            <td>${new Date(group.created_at).toLocaleDateString()}</td>
                            <td>
                                <button class="btn btn-primary" style="font-size: 0.8rem; padding: 5px 10px; margin-right: 5px;" onclick="event.stopPropagation(); viewGroupContacts(${group.id}, '${group.name}')">View Contacts</button>
                                <button class="btn" style="font-size: 0.8rem; padding: 5px 10px; background-color: #28a745; color: white;" onclick="event.stopPropagation(); openEmailModal(${group.id}, '${group.name}', ${group.actual_contact_count})">Send Email</button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            `;

            groupsTable.innerHTML = '';
            groupsTable.appendChild(table);
        }

        function displayContacts(contactsData) {
            if (contactsData.length === 0) {
                contactsTable.innerHTML = '';
                contactsTable.appendChild(emptyState);
                return;
            }

            const table = document.createElement('table');
            table.innerHTML = `
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Principal Name</th>
                        <th>Email</th>
                        <th>GDE Email</th>
                        <th>Cell Phone</th>
                        <th>Group</th>
                        <th>Created</th>
                    </tr>
                </thead>
                <tbody>
                    ${contactsData.map(contact => `
                        <tr>
                            <td>${contact.id}</td>
                            <td>${contact.principal_name || '-'}</td>
                            <td>${contact.email || '-'}</td>
                            <td>${contact.gde_email || '-'}</td>
                            <td>${contact.cell_phone || '-'}</td>
                            <td>${contact.group_name || '-'}</td>
                            <td>${new Date(contact.created_at).toLocaleDateString()}</td>
                        </tr>
                    `).join('')}
                </tbody>
            `;

            contactsTable.innerHTML = '';
            contactsTable.appendChild(table);
        }

        // Group management functions
        function selectContactSet(contactSetId) {
            selectedContactSetId = contactSetId;
            loadContactsBySet(contactSetId);

            // Find the contact set name
            const contactSet = contactSets.find(set => set.id === contactSetId);
            if (contactSet) {
                showAlert(`Viewing contacts from "${contactSet.name}"`, 'info');
            }
        }

        function showGroupCreation(contactSetId) {
            selectedContactSetId = contactSetId;
            groupsSection.style.display = 'block';
            groupSizeInput.focus();

            // Find the contact set
            const contactSet = contactSets.find(set => set.id === contactSetId);
            if (contactSet) {
                showAlert(`Create groups for "${contactSet.name}" (${contactSet.total_contacts} contacts)`, 'info');
            }
        }

        function viewGroups(contactSetId) {
            selectedContactSetId = contactSetId;
            loadGroups(contactSetId);
        }

        function viewGroupContacts(groupId, groupName) {
            loadContactsByGroup(groupId);
            showAlert(`Viewing contacts from "${groupName}"`, 'info');

            // Scroll to contacts section
            document.querySelector('.contacts-section:last-child').scrollIntoView({
                behavior: 'smooth'
            });
        }

        async function createGroups() {
            if (!selectedContactSetId) {
                showAlert('Please select a contact set first', 'error');
                return;
            }

            const groupSize = parseInt(groupSizeInput.value);
            if (!groupSize || groupSize < 1) {
                showAlert('Please enter a valid group size', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/api/contact-sets/${selectedContactSetId}/create-groups`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ groupSize })
                });

                const result = await response.json();

                if (response.ok) {
                    showAlert(`Successfully created ${result.groupCount} groups!`, 'success');
                    loadGroups(selectedContactSetId);
                    loadContactSets(); // Refresh contact sets to update group count
                    groupSizeInput.value = '';
                } else {
                    showAlert(`Error: ${result.error}`, 'error');
                }
            } catch (error) {
                console.error('Create groups error:', error);
                showAlert('Failed to create groups. Please try again.', 'error');
            }
        }

        async function clearAllContacts() {
            if (!confirm('Are you sure you want to delete all data (contacts, groups, and contact sets)? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/api/contacts`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (response.ok) {
                    showAlert(`Successfully deleted ${result.deletedContacts} contacts, ${result.deletedGroups} groups, and ${result.deletedSets} contact sets.`, 'success');
                    loadContactSets();
                    groupsSection.style.display = 'none';
                    selectedContactSetId = null;
                } else {
                    showAlert(`Error: ${result.error}`, 'error');
                }
            } catch (error) {
                console.error('Clear contacts error:', error);
                showAlert('Failed to clear data. Please try again.', 'error');
            }
        }

        function updateContactsCount(count) {
            const countText = count === 0 ? 'No contacts' : `${count} contact${count === 1 ? '' : 's'}`;
            contactsCount.textContent = countText;
            contactsCountHeader.textContent = countText;
        }

        function updateContactSetsCount(count) {
            const countText = count === 0 ? 'No contact sets' : `${count} contact set${count === 1 ? '' : 's'}`;
            contactSetsCountHeader.textContent = countText;
        }

        function updateGroupsCount(count) {
            const countText = count === 0 ? 'No groups' : `${count} group${count === 1 ? '' : 's'}`;
            groupsCountHeader.textContent = countText;
        }

        function showLoading(show) {
            if (show) {
                contactsTable.innerHTML = '';
                contactsTable.appendChild(loadingState);
                loadingState.style.display = 'block';
            } else {
                loadingState.style.display = 'none';
            }
        }

        function showAlert(message, type) {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;

            alertsContainer.appendChild(alert);

            // Auto-remove alert after 5 seconds
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 5000);
        }

        // Demo data functions
        async function createDemoData() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/create-demo-data`, {
                    method: 'POST'
                });

                const result = await response.json();

                if (response.ok) {
                    showAlert(`Demo contact set created successfully! ${result.insertedCount} contacts added.`, 'success');
                    loadContactSets();
                } else {
                    showAlert(`Error: ${result.error}`, 'error');
                }
            } catch (error) {
                console.error('Create demo data error:', error);
                showAlert('Failed to create demo data. Please try again.', 'error');
            }
        }

        // Email functionality
        let currentGroupId = null;
        let currentGroupName = '';
        let attachedFiles = [];

        function openEmailModal(groupId, groupName, contactCount) {
            currentGroupId = groupId;
            currentGroupName = groupName;

            // Update modal content
            document.getElementById('recipientCount').textContent = contactCount;
            document.getElementById('recipientPreview').textContent = `Sending to all contacts in ${groupName}`;

            // Clear form
            document.getElementById('emailSubject').value = '';
            document.getElementById('emailMessage').value = '';
            attachedFiles = [];
            updateAttachedFilesList();

            // Show modal
            document.getElementById('emailModal').style.display = 'block';
        }

        function closeEmailModal() {
            document.getElementById('emailModal').style.display = 'none';
            currentGroupId = null;
            currentGroupName = '';
            attachedFiles = [];
        }

        // Attachment handling
        const attachmentUpload = document.getElementById('attachmentUpload');
        const attachmentInput = document.getElementById('attachmentInput');

        attachmentUpload.addEventListener('click', () => {
            attachmentInput.click();
        });

        attachmentUpload.addEventListener('dragover', (e) => {
            e.preventDefault();
            attachmentUpload.classList.add('dragover');
        });

        attachmentUpload.addEventListener('dragleave', () => {
            attachmentUpload.classList.remove('dragover');
        });

        attachmentUpload.addEventListener('drop', (e) => {
            e.preventDefault();
            attachmentUpload.classList.remove('dragover');
            handleAttachmentFiles(e.dataTransfer.files);
        });

        attachmentInput.addEventListener('change', (e) => {
            handleAttachmentFiles(e.target.files);
        });

        function handleAttachmentFiles(files) {
            for (let file of files) {
                if (attachedFiles.length >= 10) {
                    showAlert('Maximum 10 attachments allowed', 'error');
                    break;
                }

                if (file.size > 10 * 1024 * 1024) {
                    showAlert(`File ${file.name} is too large. Maximum size is 10MB.`, 'error');
                    continue;
                }

                attachedFiles.push(file);
            }

            updateAttachedFilesList();
            attachmentInput.value = ''; // Clear input
        }

        function updateAttachedFilesList() {
            const container = document.getElementById('attachedFiles');
            container.innerHTML = '';

            attachedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <span>${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)</span>
                    <button onclick="removeAttachment(${index})">Remove</button>
                `;
                container.appendChild(fileItem);
            });
        }

        function removeAttachment(index) {
            attachedFiles.splice(index, 1);
            updateAttachedFilesList();
        }

        async function sendBulkEmail() {
            console.log('=== FRONTEND: SEND BULK EMAIL FUNCTION CALLED ===');
            console.log('Timestamp:', new Date().toISOString());

            const subject = document.getElementById('emailSubject').value.trim();
            const message = document.getElementById('emailMessage').value.trim();

            console.log('Form data:', {
                subject: subject,
                messageLength: message.length,
                currentGroupId: currentGroupId,
                attachedFilesCount: attachedFiles.length
            });

            if (!subject || !message) {
                console.log('ERROR: Missing subject or message');
                showAlert('Please fill in both subject and message', 'error');
                return;
            }

            if (!currentGroupId) {
                console.log('ERROR: No group selected');
                showAlert('No group selected', 'error');
                return;
            }

            console.log('=== FRONTEND: PREPARING REQUEST ===');
            const sendBtn = document.getElementById('sendEmailBtn');
            sendBtn.disabled = true;
            sendBtn.textContent = 'Sending...';

            try {
                const formData = new FormData();
                formData.append('subject', subject);
                formData.append('message', message);

                console.log('FormData created, adding attachments...');
                // Add attachments
                attachedFiles.forEach((file, index) => {
                    console.log(`Adding attachment ${index + 1}:`, {
                        name: file.name,
                        size: file.size,
                        type: file.type
                    });
                    formData.append('attachments', file);
                });

                console.log('=== FRONTEND: SENDING REQUEST ===');
                console.log('URL:', `${API_BASE_URL}/api/groups/${currentGroupId}/send-email`);
                console.log('Method: POST');
                console.log('FormData entries:');
                for (let [key, value] of formData.entries()) {
                    if (key === 'attachments') {
                        console.log(`  ${key}:`, value.name, value.size, 'bytes');
                    } else {
                        console.log(`  ${key}:`, value);
                    }
                }

                const response = await fetch(`${API_BASE_URL}/api/groups/${currentGroupId}/send-email`, {
                    method: 'POST',
                    body: formData
                });

                console.log('=== FRONTEND: RESPONSE RECEIVED ===');
                console.log('Response status:', response.status);
                console.log('Response statusText:', response.statusText);
                console.log('Response headers:', Object.fromEntries(response.headers.entries()));

                const result = await response.json();
                console.log('Response body:', result);

                if (response.ok) {
                    console.log('=== FRONTEND: SUCCESS RESPONSE ===');
                    console.log('Success result:', result);
                    showAlert(
                        `Email sent successfully! ${result.successful} sent, ${result.failed} failed out of ${result.total} total.`,
                        'success'
                    );
                    closeEmailModal();
                } else {
                    console.log('=== FRONTEND: ERROR RESPONSE ===');
                    console.log('Error status:', response.status);
                    console.log('Error result:', result);
                    console.log('Error details:', {
                        error: result.error,
                        details: result.details,
                        code: result.code,
                        timestamp: result.timestamp
                    });
                    showAlert(`Error: ${result.error}${result.details ? ' - ' + result.details : ''}`, 'error');
                }

            } catch (error) {
                console.error('=== FRONTEND: CATCH BLOCK ERROR ===');
                console.error('Error type:', error.constructor.name);
                console.error('Error message:', error.message);
                console.error('Error stack:', error.stack);
                console.error('Full error object:', error);
                showAlert('Failed to send email. Please try again. Check console for details.', 'error');
            } finally {
                console.log('=== FRONTEND: FINALLY BLOCK ===');
                sendBtn.disabled = false;
                sendBtn.textContent = 'Send Email';
            }
        }

        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('emailModal');
            if (e.target === modal) {
                closeEmailModal();
            }

            const detailsModal = document.getElementById('emailDetailsModal');
            if (e.target === detailsModal) {
                closeEmailDetailsModal();
            }
        });

        // Email History Functions
        async function loadEmailHistory(reset = false) {
            try {
                if (reset) {
                    emailHistoryOffset = 0;
                    sentEmails = [];
                }

                const response = await fetch(`${API_BASE_URL}/api/sent-emails?limit=${emailHistoryLimit}&offset=${emailHistoryOffset}`);
                const data = await response.json();

                if (response.ok) {
                    if (reset) {
                        sentEmails = data.emails;
                    } else {
                        sentEmails = [...sentEmails, ...data.emails];
                    }

                    displayEmailHistory();
                    updateEmailHistoryHeader();

                    // Show/hide load more button
                    const loadMoreBtn = document.getElementById('loadMoreBtn');
                    if (data.hasMore) {
                        loadMoreBtn.style.display = 'inline-block';
                        emailHistoryOffset += emailHistoryLimit;
                    } else {
                        loadMoreBtn.style.display = 'none';
                    }
                } else {
                    throw new Error(data.error || 'Failed to load email history');
                }
            } catch (error) {
                console.error('Error loading email history:', error);
                showAlert('Error loading email history: ' + error.message, 'error');
            }
        }

        function displayEmailHistory() {
            const tbody = document.getElementById('emailHistoryTableBody');

            if (sentEmails.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 2rem; color: #666;">
                            No emails sent yet. Send your first email to see it here!
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = sentEmails.map(email => {
                const date = new Date(email.created_at).toLocaleString();
                const successRate = email.total_recipients > 0 ?
                    Math.round((email.successful_count / email.total_recipients) * 100) : 0;

                let statusBadge = '';
                switch (email.status) {
                    case 'completed':
                        statusBadge = '<span class="status-badge status-success">✅ Completed</span>';
                        break;
                    case 'completed_with_errors':
                        statusBadge = '<span class="status-badge status-warning">⚠️ Partial</span>';
                        break;
                    case 'failed':
                        statusBadge = '<span class="status-badge status-error">❌ Failed</span>';
                        break;
                    case 'sending':
                        statusBadge = '<span class="status-badge status-info">📤 Sending</span>';
                        break;
                    default:
                        statusBadge = '<span class="status-badge status-secondary">⏳ Pending</span>';
                }

                const groupInfo = email.group_name || email.contact_set_name || 'Unknown';

                return `
                    <tr>
                        <td>${date}</td>
                        <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${email.subject}">
                            ${email.subject}
                        </td>
                        <td>${email.total_recipients}</td>
                        <td>${statusBadge}</td>
                        <td>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <div style="background: #e9ecef; border-radius: 10px; height: 8px; flex: 1; overflow: hidden;">
                                    <div style="background: ${successRate >= 80 ? '#28a745' : successRate >= 50 ? '#ffc107' : '#dc3545'}; height: 100%; width: ${successRate}%; transition: width 0.3s;"></div>
                                </div>
                                <span style="font-size: 0.9rem; color: #666;">${successRate}%</span>
                            </div>
                            <small style="color: #666;">${email.successful_count}/${email.total_recipients} sent</small>
                        </td>
                        <td style="max-width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${groupInfo}">
                            ${groupInfo}
                        </td>
                        <td>
                            <button class="btn btn-sm btn-primary" onclick="viewEmailDetails(${email.id})" title="View Details">
                                👁️ View
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function updateEmailHistoryHeader() {
            const header = document.getElementById('emailHistoryCountHeader');
            if (sentEmails.length === 0) {
                header.textContent = 'No emails sent yet';
            } else {
                header.textContent = `${sentEmails.length} email${sentEmails.length !== 1 ? 's' : ''} sent`;
            }
        }

        async function refreshEmailHistory() {
            await loadEmailHistory(true);
        }

        async function loadMoreEmails() {
            await loadEmailHistory(false);
        }

        async function viewEmailDetails(emailId) {
            try {
                const response = await fetch(`${API_BASE_URL}/api/sent-emails/${emailId}`);
                const emailDetails = await response.json();

                if (response.ok) {
                    displayEmailDetailsModal(emailDetails);
                } else {
                    throw new Error(emailDetails.error || 'Failed to load email details');
                }
            } catch (error) {
                console.error('Error loading email details:', error);
                showAlert('Error loading email details: ' + error.message, 'error');
            }
        }

        function displayEmailDetailsModal(email) {
            const modal = document.getElementById('emailDetailsModal');
            const content = document.getElementById('emailDetailsContent');

            const date = new Date(email.created_at).toLocaleString();
            const completedDate = email.completed_at ? new Date(email.completed_at).toLocaleString() : 'Not completed';

            let statusBadge = '';
            switch (email.status) {
                case 'completed':
                    statusBadge = '<span class="status-badge status-success">✅ Completed</span>';
                    break;
                case 'completed_with_errors':
                    statusBadge = '<span class="status-badge status-warning">⚠️ Completed with Errors</span>';
                    break;
                case 'failed':
                    statusBadge = '<span class="status-badge status-error">❌ Failed</span>';
                    break;
                case 'sending':
                    statusBadge = '<span class="status-badge status-info">📤 Sending</span>';
                    break;
                default:
                    statusBadge = '<span class="status-badge status-secondary">⏳ Pending</span>';
            }

            content.innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                    <div>
                        <h3>📧 Email Information</h3>
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px;">
                            <p><strong>Subject:</strong> ${email.subject}</p>
                            <p><strong>Status:</strong> ${statusBadge}</p>
                            <p><strong>Sent Date:</strong> ${date}</p>
                            <p><strong>Completed:</strong> ${completedDate}</p>
                            <p><strong>Group/Set:</strong> ${email.group_name || email.contact_set_name || 'Unknown'}</p>
                            ${email.attachment_names ? `<p><strong>Attachments:</strong> ${email.attachment_names}</p>` : ''}
                        </div>
                    </div>
                    <div>
                        <h3>📊 Statistics</h3>
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px;">
                            <p><strong>Total Recipients:</strong> ${email.total_recipients}</p>
                            <p><strong>Successfully Sent:</strong> <span style="color: #28a745;">${email.successful_count}</span></p>
                            <p><strong>Failed:</strong> <span style="color: #dc3545;">${email.failed_count}</span></p>
                            <p><strong>Success Rate:</strong> ${email.total_recipients > 0 ? Math.round((email.successful_count / email.total_recipients) * 100) : 0}%</p>
                        </div>
                    </div>
                </div>

                <div style="margin-bottom: 2rem;">
                    <h3>💬 Message Content</h3>
                    <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; white-space: pre-wrap; max-height: 200px; overflow-y: auto;">
                        ${email.message}
                    </div>
                </div>

                <div>
                    <h3>👥 Recipients (${email.recipients ? email.recipients.length : 0})</h3>
                    <div style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; border-radius: 8px;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead style="background: #f8f9fa; position: sticky; top: 0;">
                                <tr>
                                    <th style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">Name</th>
                                    <th style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">Email</th>
                                    <th style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">Status</th>
                                    <th style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">Sent At</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${email.recipients ? email.recipients.map(recipient => {
                                    const sentAt = recipient.sent_at ? new Date(recipient.sent_at).toLocaleString() : 'Not sent';
                                    let recipientStatus = '';
                                    switch (recipient.status) {
                                        case 'sent':
                                            recipientStatus = '<span style="color: #28a745;">✅ Sent</span>';
                                            break;
                                        case 'failed':
                                            recipientStatus = `<span style="color: #dc3545;" title="${recipient.error_message || 'Unknown error'}">❌ Failed</span>`;
                                            break;
                                        default:
                                            recipientStatus = '<span style="color: #6c757d;">⏳ Pending</span>';
                                    }

                                    return `
                                        <tr>
                                            <td style="padding: 8px; border-bottom: 1px solid #eee;">${recipient.recipient_name || 'Unknown'}</td>
                                            <td style="padding: 8px; border-bottom: 1px solid #eee;">${recipient.recipient_email}</td>
                                            <td style="padding: 8px; border-bottom: 1px solid #eee;">${recipientStatus}</td>
                                            <td style="padding: 8px; border-bottom: 1px solid #eee;">${sentAt}</td>
                                        </tr>
                                    `;
                                }).join('') : '<tr><td colspan="4" style="padding: 1rem; text-align: center; color: #666;">No recipients found</td></tr>'}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;

            modal.style.display = 'block';
        }

        function closeEmailDetailsModal() {
            const modal = document.getElementById('emailDetailsModal');
            modal.style.display = 'none';
        }

        // Load email history when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadEmailHistory(true);
        });
    </script>
</body>
</html>