<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sent Emails - Multi Email Sender</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #4a5568;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            font-weight: 700;
        }

        .header p {
            color: #718096;
            font-size: 1.1rem;
        }

        .nav-links {
            margin-top: 1rem;
        }

        .nav-links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 1rem;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: background-color 0.3s;
        }

        .nav-links a:hover {
            background-color: rgba(102, 126, 234, 0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-card h3 {
            color: #4a5568;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .stat-card p {
            color: #718096;
            font-size: 1rem;
        }

        .emails-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .section-header h2 {
            color: #4a5568;
            font-size: 1.8rem;
            font-weight: 600;
        }

        .controls {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-secondary:hover {
            background: #cbd5e0;
        }

        .table-container {
            overflow-x: auto;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        th {
            background: #f7fafc;
            font-weight: 600;
            color: #4a5568;
            position: sticky;
            top: 0;
        }

        tr:hover {
            background: #f7fafc;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            white-space: nowrap;
        }

        .status-success {
            background-color: #c6f6d5;
            color: #22543d;
        }

        .status-warning {
            background-color: #fef5e7;
            color: #c05621;
        }

        .status-error {
            background-color: #fed7d7;
            color: #c53030;
        }

        .status-info {
            background-color: #bee3f8;
            color: #2c5282;
        }

        .status-secondary {
            background-color: #e2e8f0;
            color: #4a5568;
        }

        .progress-bar {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .progress-track {
            background: #e2e8f0;
            border-radius: 10px;
            height: 8px;
            flex: 1;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 0.875rem;
            color: #718096;
            min-width: 40px;
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        .loading {
            text-align: center;
            padding: 3rem;
            color: #718096;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #718096;
        }

        .empty-state h3 {
            margin-bottom: 1rem;
            color: #4a5568;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .section-header {
                flex-direction: column;
                align-items: stretch;
            }

            .controls {
                justify-content: center;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            th, td {
                padding: 0.75rem 0.5rem;
                font-size: 0.875rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📧 Sent Emails</h1>
            <p>Track and monitor all your email campaigns</p>
            <div class="nav-links">
                <a href="index.html">← Back to Dashboard</a>
                <a href="#" onclick="refreshData()">🔄 Refresh</a>
            </div>
        </div>

        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <h3 id="totalEmailsCount">-</h3>
                <p>Total Emails Sent</p>
            </div>
            <div class="stat-card">
                <h3 id="totalRecipientsCount">-</h3>
                <p>Total Recipients</p>
            </div>
            <div class="stat-card">
                <h3 id="successRatePercent">-</h3>
                <p>Overall Success Rate</p>
            </div>
            <div class="stat-card">
                <h3 id="recentEmailsCount">-</h3>
                <p>Emails This Week</p>
            </div>
        </div>

        <div class="emails-section">
            <div class="section-header">
                <h2>Email History</h2>
                <div class="controls">
                    <button class="btn btn-primary" onclick="refreshData()">
                        🔄 Refresh Data
                    </button>
                    <button class="btn btn-secondary" onclick="loadMoreEmails()" id="loadMoreBtn" style="display: none;">
                        Load More
                    </button>
                </div>
            </div>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Date & Time</th>
                            <th>Subject</th>
                            <th>Recipients</th>
                            <th>Status</th>
                            <th>Success Rate</th>
                            <th>Group/Set</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="emailsTableBody">
                        <tr>
                            <td colspan="7" class="loading">
                                <div>📡 Loading email data...</div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <!-- Email Details Modal -->
    <div id="emailDetailsModal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
        <div class="modal-content" style="background: white; margin: 5% auto; padding: 0; width: 90%; max-width: 900px; border-radius: 15px; box-shadow: 0 8px 32px rgba(0,0,0,0.3); max-height: 80vh; overflow-y: auto;">
            <div class="modal-header" style="padding: 1.5rem; border-bottom: 1px solid #e2e8f0; display: flex; justify-content: space-between; align-items: center;">
                <h2 style="color: #4a5568; margin: 0;">📧 Email Details</h2>
                <button class="close" onclick="closeEmailDetailsModal()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; color: #718096;">&times;</button>
            </div>
            <div class="modal-body" style="padding: 1.5rem;">
                <div id="emailDetailsContent">
                    <div style="text-align: center; padding: 2rem; color: #718096;">
                        Loading email details...
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="padding: 1.5rem; border-top: 1px solid #e2e8f0; text-align: right;">
                <button class="btn btn-secondary" onclick="closeEmailDetailsModal()">Close</button>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let sentEmails = [];
        let emailHistoryOffset = 0;
        let emailHistoryLimit = 50;
        let stats = {
            totalEmails: 0,
            totalRecipients: 0,
            totalSuccessful: 0,
            recentEmails: 0
        };

        // API base URL
        const API_BASE_URL = window.location.hostname === 'localhost' && window.location.port === '3000'
            ? ''
            : 'http://localhost:3000';

        // Load email data when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadEmailData(true);
        });

        async function loadEmailData(reset = false) {
            try {
                if (reset) {
                    emailHistoryOffset = 0;
                    sentEmails = [];
                }

                const response = await fetch(`${API_BASE_URL}/api/sent-emails?limit=${emailHistoryLimit}&offset=${emailHistoryOffset}`);
                const data = await response.json();

                if (response.ok) {
                    if (reset) {
                        sentEmails = data.emails;
                    } else {
                        sentEmails = [...sentEmails, ...data.emails];
                    }

                    calculateStats();
                    displayStats();
                    displayEmails();

                    // Show/hide load more button
                    const loadMoreBtn = document.getElementById('loadMoreBtn');
                    if (data.hasMore) {
                        loadMoreBtn.style.display = 'inline-flex';
                        emailHistoryOffset += emailHistoryLimit;
                    } else {
                        loadMoreBtn.style.display = 'none';
                    }
                } else {
                    throw new Error(data.error || 'Failed to load email data');
                }
            } catch (error) {
                console.error('Error loading email data:', error);
                displayError('Failed to load email data. Please check your connection and try again.');
            }
        }

        function calculateStats() {
            stats.totalEmails = sentEmails.length;
            stats.totalRecipients = sentEmails.reduce((sum, email) => sum + (email.total_recipients || 0), 0);
            stats.totalSuccessful = sentEmails.reduce((sum, email) => sum + (email.successful_count || 0), 0);

            // Calculate emails from the last 7 days
            const oneWeekAgo = new Date();
            oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
            stats.recentEmails = sentEmails.filter(email =>
                new Date(email.created_at) >= oneWeekAgo
            ).length;
        }

        function displayStats() {
            document.getElementById('totalEmailsCount').textContent = stats.totalEmails.toLocaleString();
            document.getElementById('totalRecipientsCount').textContent = stats.totalRecipients.toLocaleString();

            const successRate = stats.totalRecipients > 0 ?
                Math.round((stats.totalSuccessful / stats.totalRecipients) * 100) : 0;
            document.getElementById('successRatePercent').textContent = successRate + '%';

            document.getElementById('recentEmailsCount').textContent = stats.recentEmails.toLocaleString();
        }

        function displayEmails() {
            const tbody = document.getElementById('emailsTableBody');

            if (sentEmails.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="empty-state">
                            <h3>No emails found</h3>
                            <p>Start sending emails to see them appear here.</p>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = sentEmails.map(email => {
                const date = new Date(email.created_at).toLocaleString();
                const successRate = email.total_recipients > 0 ?
                    Math.round((email.successful_count / email.total_recipients) * 100) : 0;

                let statusBadge = '';
                let progressColor = '';

                switch (email.status) {
                    case 'completed':
                        statusBadge = '<span class="status-badge status-success">✅ Completed</span>';
                        progressColor = '#48bb78';
                        break;
                    case 'completed_with_errors':
                        statusBadge = '<span class="status-badge status-warning">⚠️ Partial Success</span>';
                        progressColor = '#ed8936';
                        break;
                    case 'failed':
                        statusBadge = '<span class="status-badge status-error">❌ Failed</span>';
                        progressColor = '#f56565';
                        break;
                    case 'sending':
                        statusBadge = '<span class="status-badge status-info">📤 Sending</span>';
                        progressColor = '#4299e1';
                        break;
                    default:
                        statusBadge = '<span class="status-badge status-secondary">⏳ Pending</span>';
                        progressColor = '#a0aec0';
                }

                const groupInfo = email.group_name || email.contact_set_name || 'Direct Send';

                return `
                    <tr>
                        <td style="min-width: 150px;">
                            <div style="font-weight: 500;">${date.split(',')[0]}</div>
                            <div style="font-size: 0.875rem; color: #718096;">${date.split(',')[1]}</div>
                        </td>
                        <td style="max-width: 250px;">
                            <div style="font-weight: 500; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${email.subject}">
                                ${email.subject}
                            </div>
                            ${email.attachment_count > 0 ? `<div style="font-size: 0.75rem; color: #718096;">📎 ${email.attachment_count} attachment${email.attachment_count > 1 ? 's' : ''}</div>` : ''}
                        </td>
                        <td>
                            <div style="font-weight: 500;">${email.total_recipients}</div>
                            <div style="font-size: 0.875rem; color: #718096;">recipients</div>
                        </td>
                        <td>${statusBadge}</td>
                        <td style="min-width: 120px;">
                            <div class="progress-bar">
                                <div class="progress-track">
                                    <div class="progress-fill" style="background: ${progressColor}; width: ${successRate}%;"></div>
                                </div>
                                <span class="progress-text">${successRate}%</span>
                            </div>
                            <div style="font-size: 0.75rem; color: #718096; margin-top: 2px;">
                                ${email.successful_count}/${email.total_recipients} sent
                            </div>
                        </td>
                        <td style="max-width: 150px;">
                            <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${groupInfo}">
                                ${groupInfo}
                            </div>
                        </td>
                        <td>
                            <button class="btn btn-primary btn-sm" onclick="viewEmailDetails(${email.id})" title="View Details">
                                👁️ View
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function displayError(message) {
            const tbody = document.getElementById('emailsTableBody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" style="text-align: center; padding: 2rem; color: #f56565;">
                        ❌ ${message}
                    </td>
                </tr>
            `;
        }

        async function refreshData() {
            await loadEmailData(true);
        }

        async function loadMoreEmails() {
            await loadEmailData(false);
        }

        async function viewEmailDetails(emailId) {
            try {
                const response = await fetch(`${API_BASE_URL}/api/sent-emails/${emailId}`);
                const emailDetails = await response.json();

                if (response.ok) {
                    displayEmailDetailsModal(emailDetails);
                } else {
                    throw new Error(emailDetails.error || 'Failed to load email details');
                }
            } catch (error) {
                console.error('Error loading email details:', error);
                alert('Error loading email details: ' + error.message);
            }
        }

        function displayEmailDetailsModal(email) {
            const modal = document.getElementById('emailDetailsModal');
            const content = document.getElementById('emailDetailsContent');

            const date = new Date(email.created_at).toLocaleString();
            const completedDate = email.completed_at ? new Date(email.completed_at).toLocaleString() : 'Not completed';

            let statusBadge = '';
            switch (email.status) {
                case 'completed':
                    statusBadge = '<span class="status-badge status-success">✅ Completed</span>';
                    break;
                case 'completed_with_errors':
                    statusBadge = '<span class="status-badge status-warning">⚠️ Completed with Errors</span>';
                    break;
                case 'failed':
                    statusBadge = '<span class="status-badge status-error">❌ Failed</span>';
                    break;
                case 'sending':
                    statusBadge = '<span class="status-badge status-info">📤 Sending</span>';
                    break;
                default:
                    statusBadge = '<span class="status-badge status-secondary">⏳ Pending</span>';
            }

            const successRate = email.total_recipients > 0 ?
                Math.round((email.successful_count / email.total_recipients) * 100) : 0;

            content.innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                    <div>
                        <h3 style="color: #4a5568; margin-bottom: 1rem;">📧 Email Information</h3>
                        <div style="background: #f7fafc; padding: 1.5rem; border-radius: 12px; border: 1px solid #e2e8f0;">
                            <p style="margin-bottom: 0.75rem;"><strong>Subject:</strong> ${email.subject}</p>
                            <p style="margin-bottom: 0.75rem;"><strong>Status:</strong> ${statusBadge}</p>
                            <p style="margin-bottom: 0.75rem;"><strong>Sent Date:</strong> ${date}</p>
                            <p style="margin-bottom: 0.75rem;"><strong>Completed:</strong> ${completedDate}</p>
                            <p style="margin-bottom: 0.75rem;"><strong>Group/Set:</strong> ${email.group_name || email.contact_set_name || 'Direct Send'}</p>
                            ${email.attachment_names ? `<p style="margin-bottom: 0;"><strong>Attachments:</strong> ${email.attachment_names}</p>` : ''}
                        </div>
                    </div>
                    <div>
                        <h3 style="color: #4a5568; margin-bottom: 1rem;">📊 Performance Metrics</h3>
                        <div style="background: #f7fafc; padding: 1.5rem; border-radius: 12px; border: 1px solid #e2e8f0;">
                            <p style="margin-bottom: 0.75rem;"><strong>Total Recipients:</strong> ${email.total_recipients}</p>
                            <p style="margin-bottom: 0.75rem;"><strong>Successfully Sent:</strong> <span style="color: #48bb78; font-weight: 600;">${email.successful_count}</span></p>
                            <p style="margin-bottom: 0.75rem;"><strong>Failed:</strong> <span style="color: #f56565; font-weight: 600;">${email.failed_count}</span></p>
                            <p style="margin-bottom: 0.75rem;"><strong>Success Rate:</strong> <span style="color: ${successRate >= 80 ? '#48bb78' : successRate >= 50 ? '#ed8936' : '#f56565'}; font-weight: 600;">${successRate}%</span></p>
                            ${email.attachment_count > 0 ? `<p style="margin-bottom: 0;"><strong>Attachments:</strong> ${email.attachment_count} file${email.attachment_count > 1 ? 's' : ''}</p>` : ''}
                        </div>
                    </div>
                </div>

                <div style="margin-bottom: 2rem;">
                    <h3 style="color: #4a5568; margin-bottom: 1rem;">💬 Message Content</h3>
                    <div style="background: #f7fafc; padding: 1.5rem; border-radius: 12px; border: 1px solid #e2e8f0; white-space: pre-wrap; max-height: 200px; overflow-y: auto; font-family: inherit;">
                        ${email.message}
                    </div>
                </div>

                <div>
                    <h3 style="color: #4a5568; margin-bottom: 1rem;">👥 Recipients (${email.recipients ? email.recipients.length : 0})</h3>
                    <div style="max-height: 300px; overflow-y: auto; border: 1px solid #e2e8f0; border-radius: 12px; background: white;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead style="background: #f7fafc; position: sticky; top: 0;">
                                <tr>
                                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e2e8f0; font-weight: 600; color: #4a5568;">Name</th>
                                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e2e8f0; font-weight: 600; color: #4a5568;">Email</th>
                                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e2e8f0; font-weight: 600; color: #4a5568;">Status</th>
                                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e2e8f0; font-weight: 600; color: #4a5568;">Sent At</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${email.recipients ? email.recipients.map(recipient => {
                                    const sentAt = recipient.sent_at ? new Date(recipient.sent_at).toLocaleString() : 'Not sent';
                                    let recipientStatus = '';
                                    switch (recipient.status) {
                                        case 'sent':
                                            recipientStatus = '<span style="color: #48bb78; font-weight: 500;">✅ Sent</span>';
                                            break;
                                        case 'failed':
                                            recipientStatus = `<span style="color: #f56565; font-weight: 500;" title="${recipient.error_message || 'Unknown error'}">❌ Failed</span>`;
                                            break;
                                        default:
                                            recipientStatus = '<span style="color: #a0aec0; font-weight: 500;">⏳ Pending</span>';
                                    }

                                    return `
                                        <tr style="border-bottom: 1px solid #f7fafc;">
                                            <td style="padding: 12px;">${recipient.recipient_name || 'Unknown'}</td>
                                            <td style="padding: 12px;">${recipient.recipient_email}</td>
                                            <td style="padding: 12px;">${recipientStatus}</td>
                                            <td style="padding: 12px; color: #718096;">${sentAt}</td>
                                        </tr>
                                    `;
                                }).join('') : '<tr><td colspan="4" style="padding: 2rem; text-align: center; color: #a0aec0;">No recipients found</td></tr>'}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;

            modal.style.display = 'block';
        }

        function closeEmailDetailsModal() {
            const modal = document.getElementById('emailDetailsModal');
            modal.style.display = 'none';
        }

        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('emailDetailsModal');
            if (e.target === modal) {
                closeEmailDetailsModal();
            }
        });
    </script>
</body>
</html>
