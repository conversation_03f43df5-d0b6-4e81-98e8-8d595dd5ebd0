const nodemailer = require('nodemailer');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

// Create transporter
const transporter = nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
    }
});

// Verify transporter configuration
transporter.verify((error, success) => {
    if (error) {
        console.error('Email transporter verification failed:', error);
    } else {
        console.log('Email transporter is ready to send emails');
    }
});

// Send bulk email to multiple recipients
async function sendBulkEmail(recipients, subject, message, attachments = []) {
    console.log('=== BULK EMAIL FUNCTION CALLED ===');
    console.log('Timestamp:', new Date().toISOString());
    console.log('Recipients count:', recipients.length);
    console.log('Subject:', subject);
    console.log('Message length:', message.length);
    console.log('Attachments count:', attachments.length);

    // Log environment variables (without sensitive data)
    console.log('Email configuration check:', {
        host: process.env.EMAIL_HOST,
        port: process.env.EMAIL_PORT,
        secure: process.env.EMAIL_SECURE,
        user: process.env.EMAIL_USER ? 'SET' : 'NOT SET',
        pass: process.env.EMAIL_PASS ? 'SET' : 'NOT SET',
        from: process.env.EMAIL_FROM
    });

    const results = {
        successful: [],
        failed: [],
        total: recipients.length
    };

    // Process attachments
    const processedAttachments = attachments.map(attachment => ({
        filename: attachment.originalname,
        path: attachment.path
    }));
    console.log('Processed attachments:', processedAttachments);

    // Log first few recipients for debugging
    console.log('Sample recipients:', recipients.slice(0, 3).map(r => ({
        email: r.email,
        name: r.principal_name
    })));

    console.log('=== STARTING EMAIL SENDING LOOP ===');

    for (let i = 0; i < recipients.length; i++) {
        const recipient = recipients[i];
        console.log(`=== SENDING EMAIL ${i + 1}/${recipients.length} ===`);
        console.log(`Recipient: ${recipient.email} (${recipient.principal_name})`);
        console.log('Recipient details:', {
            id: recipient.id,
            email: recipient.email,
            name: recipient.principal_name,
            phone: recipient.cell_phone
        });

        try {
            console.log('Creating mail options...');
            const mailOptions = {
                from: process.env.EMAIL_FROM,
                to: recipient.email,
                subject: subject,
                html: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <h2 style="color: #333;">Hello ${recipient.principal_name || 'there'},</h2>
                        <div style="line-height: 1.6; color: #555;">
                            ${message.replace(/\n/g, '<br>')}
                        </div>
                        <hr style="margin: 20px 0; border: none; border-top: 1px solid #eee;">
                        <p style="font-size: 12px; color: #888;">
                            This email was sent to: ${recipient.email}<br>
                            ${recipient.cell_phone ? `Phone: ${recipient.cell_phone}` : ''}
                        </p>
                    </div>
                `,
                attachments: processedAttachments
            };

            console.log('Mail options created:', {
                from: mailOptions.from,
                to: mailOptions.to,
                subject: mailOptions.subject,
                attachmentCount: mailOptions.attachments.length
            });

            console.log('Attempting to send email via transporter...');
            const sendResult = await transporter.sendMail(mailOptions);
            console.log('Email sent successfully! Result:', {
                messageId: sendResult.messageId,
                response: sendResult.response
            });

            results.successful.push({
                email: recipient.email,
                name: recipient.principal_name,
                messageId: sendResult.messageId
            });

            console.log(`Email ${i + 1}/${recipients.length} sent successfully`);

            // Add a small delay to avoid overwhelming the email service
            await new Promise(resolve => setTimeout(resolve, 100));

        } catch (error) {
            console.error(`=== EMAIL SEND FAILED for ${recipient.email} ===`);
            console.error('Error type:', error.constructor.name);
            console.error('Error message:', error.message);
            console.error('Error code:', error.code);
            console.error('Full error object:', error);
            console.error('Email error details:', {
                message: error.message,
                code: error.code,
                command: error.command,
                response: error.response,
                responseCode: error.responseCode,
                errno: error.errno,
                syscall: error.syscall
            });

            // Log specific error types
            if (error.code === 'EAUTH') {
                console.error('AUTHENTICATION ERROR: Check email username/password');
            } else if (error.code === 'ECONNECTION' || error.code === 'ENOTFOUND') {
                console.error('CONNECTION ERROR: Cannot reach email server');
            } else if (error.code === 'ETIMEDOUT') {
                console.error('TIMEOUT ERROR: Email server not responding');
            }

            results.failed.push({
                email: recipient.email,
                name: recipient.principal_name,
                error: error.message,
                code: error.code,
                response: error.response,
                timestamp: new Date().toISOString()
            });

            console.log(`Email ${i + 1}/${recipients.length} failed`);
        }
    }

    console.log('=== EMAIL SENDING LOOP COMPLETED ===');

    // Clean up uploaded attachment files
    for (const attachment of attachments) {
        try {
            if (fs.existsSync(attachment.path)) {
                fs.unlinkSync(attachment.path);
            }
        } catch (error) {
            console.error('Error cleaning up attachment:', error);
        }
    }

    return results;
}

// Send test email
async function sendTestEmail(to, subject = 'Test Email', message = 'This is a test email.') {
    try {
        const mailOptions = {
            from: process.env.EMAIL_FROM,
            to: to,
            subject: subject,
            html: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <h2 style="color: #333;">Test Email</h2>
                    <p style="line-height: 1.6; color: #555;">${message}</p>
                    <p style="font-size: 12px; color: #888;">
                        Sent from Multi Email Sender Application
                    </p>
                </div>
            `
        };

        const result = await transporter.sendMail(mailOptions);
        return { success: true, messageId: result.messageId };
    } catch (error) {
        console.error('Test email failed:', error);
        return { success: false, error: error.message };
    }
}

module.exports = {
    sendBulkEmail,
    sendTestEmail
};
